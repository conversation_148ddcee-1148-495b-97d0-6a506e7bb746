defmodule Cypridina.Repo.Migrations.CreateScratchCardRewardConfigs do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    drop constraint(:scratch_card_task_levels, "scratch_card_task_levels_activity_id_fkey")

    alter table(:scratch_card_task_levels) do
      modify :activity_id,
             references(:scratch_card_activities,
               column: :id,
               name: "scratch_card_task_levels_activity_id_fkey",
               type: :uuid,
               prefix: "public"
             )
    end

    alter table(:vip_gifts) do
      modify :claim_limit, :bigint, default: nil
      modify :reward_frequency, :bigint, default: nil
    end

    drop_if_exists unique_index(:vip_gifts, [:vip_level],
                     name: "vip_gifts_unique_vip_level_index"
                   )

    drop constraint(:scratch_card_task_rounds, "scratch_card_task_rounds_activity_id_fkey")

    alter table(:scratch_card_task_rounds) do
      modify :activity_id,
             references(:scratch_card_activities,
               column: :id,
               name: "scratch_card_task_rounds_activity_id_fkey",
               type: :uuid,
               prefix: "public"
             )
    end

    create table(:scratch_card_reward_configs, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :activity_title, :text, null: false
      add :claimable_count, :bigint, null: false, default: 30
      add :reward1_probability, :decimal, null: false, default: "30"
      add :reward2_probability, :decimal, null: false, default: "30"
      add :reward3_probability, :decimal, null: false, default: "40"
      add :status, :text, null: false, default: "enabled"

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :activity_id, :uuid
    end

    create unique_index(:scratch_card_reward_configs, [:activity_id],
             name: "scratch_card_reward_configs_unique_activity_config_index"
           )



    create unique_index(:scratch_card_level_rewards, [:task_level_id, :sort_order],
             name: "scratch_card_level_rewards_unique_level_reward_sort_index"
           )
  end

  def down do
    drop_if_exists unique_index(:scratch_card_level_rewards, [:task_level_id, :sort_order],
                     name: "scratch_card_level_rewards_unique_level_reward_sort_index"
                   )

    alter table(:recharge_wheels) do
      remove :end_date
      remove :start_date
      remove :jackpot_pool
      remove :description
      remove :title
    end

    drop_if_exists unique_index(:scratch_card_reward_configs, [:activity_id],
                     name: "scratch_card_reward_configs_unique_activity_config_index"
                   )

    drop constraint(:scratch_card_reward_configs, "scratch_card_reward_configs_activity_id_fkey")

    drop table(:scratch_card_reward_configs)

    drop constraint(:scratch_card_task_rounds, "scratch_card_task_rounds_activity_id_fkey")

    alter table(:scratch_card_task_rounds) do
      modify :activity_id,
             references(:scratch_card_activities,
               column: :id,
               name: "scratch_card_task_rounds_activity_id_fkey",
               type: :uuid,
               prefix: "public"
             )
    end

    create unique_index(:vip_gifts, [:vip_level], name: "vip_gifts_unique_vip_level_index")

    alter table(:vip_gifts) do
      modify :reward_frequency, :bigint, default: 0
      modify :claim_limit, :bigint, default: 0
    end

    drop constraint(:scratch_card_task_levels, "scratch_card_task_levels_activity_id_fkey")

    alter table(:scratch_card_task_levels) do
      modify :activity_id,
             references(:scratch_card_activities,
               column: :id,
               name: "scratch_card_task_levels_activity_id_fkey",
               type: :uuid,
               prefix: "public"
             )
    end
  end
end

# 测试删除功能的脚本

IO.puts("=== 测试邀请现金活动删除功能 ===")

# 1. 创建测试活动
IO.puts("\n1. 创建测试活动...")
{:ok, activity} = Teen.ActivitySystem.InviteCashActivity
|> Ash.Changeset.for_create(:create, %{
  title: "测试删除活动",
  description: "用于测试删除功能的活动",
  total_reward: 1000,
  initial_min: 10,
  initial_max: 100,
  start_date: ~D[2024-01-01],
  end_date: ~D[2024-12-31]
})
|> Ash.create!()

IO.puts("创建测试活动成功: #{activity.id}")

# 2. 为该活动创建奖励配置
IO.puts("\n2. 创建奖励配置...")
{:ok, config1} = Teen.ActivitySystem.InviteRewardConfig
|> Ash.Changeset.for_create(:create, %{
  activity_id: activity.id,
  round_number: 1,
  task_type: "invite_friends",
  task_target: 1,
  reward_amount: 100
})
|> Ash.create!()

{:ok, config2} = Teen.ActivitySystem.InviteRewardConfig
|> Ash.Changeset.for_create(:create, %{
  activity_id: activity.id,
  round_number: 2,
  task_type: "invite_friends",
  task_target: 3,
  reward_amount: 200
})
|> Ash.create!()

IO.puts("创建奖励配置成功: #{config1.id}, #{config2.id}")

# 3. 验证数据存在
IO.puts("\n3. 验证数据存在...")
found_activity = Teen.ActivitySystem.InviteCashActivity
|> Ash.Query.for_read(:by_id, %{id: activity.id})
|> Ash.read_one!()
IO.puts("活动存在: #{found_activity.title}")

configs = Teen.ActivitySystem.InviteRewardConfig
|> Ash.Query.for_read(:list_by_activity, %{activity_id: activity.id})
|> Ash.read!()
IO.puts("找到 #{length(configs)} 个奖励配置")

# 4. 测试删除活动
IO.puts("\n4. 测试删除活动...")
case activity |> Ash.Changeset.for_destroy(:destroy) |> Ash.destroy() do
  {:ok, deleted_activity} ->
    IO.puts("删除成功: #{deleted_activity.id}")
  {:error, error} ->
    IO.puts("删除失败: #{inspect(error)}")
end

# 5. 验证删除结果
IO.puts("\n5. 验证删除结果...")

# 检查活动是否被删除
try do
  Teen.ActivitySystem.InviteCashActivity
  |> Ash.Query.for_read(:by_id, %{id: activity.id})
  |> Ash.read_one!()
  IO.puts("❌ 活动仍然存在")
rescue
  Ash.Error.Query.NotFound ->
    IO.puts("✅ 活动已被成功删除")
  error ->
    IO.puts("查询失败: #{inspect(error)}")
end

# 检查奖励配置是否被删除
remaining_configs = Teen.ActivitySystem.InviteRewardConfig
|> Ash.Query.for_read(:list_by_activity, %{activity_id: activity.id})
|> Ash.read!()

if Enum.empty?(remaining_configs) do
  IO.puts("✅ 所有奖励配置已被删除")
else
  IO.puts("❌ 仍有 #{length(remaining_configs)} 个奖励配置未被删除")
end

IO.puts("\n=== 测试完成 ===")

defmodule Teen.Live.ActivitySystem.InviteRewardConfigLive do
  @moduledoc """
  邀请奖励配置管理页面
  """

  use Backpex.LiveResource,
    adapter: Backpex.Adapters.Ash,
    adapter_config: [
      resource: Teen.ActivitySystem.InviteRewardConfig
    ],
    layout: {Teen.Layouts, :admin}

  @impl Backpex.LiveResource
  def singular_name, do: "邀请奖励配置"

  @impl Backpex.LiveResource
  def plural_name, do: "邀请奖励配置"

  @impl Backpex.LiveResource
  def fields do
    %{
      id: %{
        module: Backpex.Fields.Text,
        label: "ID",
        readonly: true,
        only: [:index, :show]
      },
      activity_id: %{
        module: Backpex.Fields.BelongsTo,
        label: "关联活动",
        display_field: :title,
        live_resource: Teen.Live.ActivitySystem.InviteCashActivityLive
      },
      round_number: %{
        module: Backpex.Fields.Number,
        label: "轮次编号",
        min: "1",
        step: "1"
      },
      task_type: %{
        module: Backpex.Fields.Select,
        label: "任务类型",
        options: [
          {"邀请注册", :invite_register},
          {"邀请充值", :invite_recharge},
          {"邀请游戏", :invite_play},
          {"邀请留存", :invite_retention}
        ],
        default: fn _assigns -> :invite_register end
      },
      reward_type: %{
        module: Backpex.Fields.Select,
        label: "奖励类型",
        options: [
          {"金币", :coins},
          {"积分", :points},
          {"现金", :cash},
          {"转盘次数", :wheel_spins},
          {"道具", :items}
        ],
        default: fn _assigns -> :coins end
      },
      reward_amount: %{
        module: Backpex.Fields.Number,
        label: "固定奖励金额（分）",
        step: "0.01",
        min: "0",
        placeholder: "留空则使用随机范围"
      },
      min_reward: %{
        module: Backpex.Fields.Number,
        label: "最小奖励金额（分）",
        step: "0.01",
        min: "0"
      },
      max_reward: %{
        module: Backpex.Fields.Number,
        label: "最大奖励金额（分）",
        step: "0.01",
        min: "0"
      },
      required_progress: %{
        module: Backpex.Fields.Number,
        label: "所需进度",
        min: "1",
        step: "1",
        default: fn _assigns -> 1 end
      },
      probability: %{
        module: Backpex.Fields.Number,
        label: "获得概率",
        step: "0.01",
        min: "0",
        max: "1",
        default: fn _assigns -> 1.0 end
      },
      description: %{
        module: Backpex.Fields.Textarea,
        label: "奖励描述",
        except: [:index]
      },
      sort_order: %{
        module: Backpex.Fields.Number,
        label: "排序顺序",
        min: "0",
        step: "1",
        default: fn _assigns -> 0 end
      },
      inserted_at: %{
        module: Backpex.Fields.DateTime,
        label: "创建时间",
        readonly: true,
        only: [:index, :show]
      },
      updated_at: %{
        module: Backpex.Fields.DateTime,
        label: "更新时间",
        readonly: true,
        only: [:index, :show]
      }
    }
  end

  @impl Backpex.LiveResource
  def can?(assigns, action, item) do
    # 允许所有操作，包括删除
    case action do
      :delete -> true
      :edit -> true
      :show -> true
      :create -> true
      :index -> true
      _ -> true
    end
  end

  @impl Backpex.LiveResource
  def resource_actions do
    [
      delete_by_activity: %{
        module: Teen.ResourceActions.DeleteByActivity,
        label: "按活动批量删除",
        icon: "hero-trash",
        confirm_label: "确认批量删除",
        confirm_text: "此操作将删除指定活动的所有奖励配置，确定要继续吗？",
        fields: []
      }
    ]
  end

  @impl Backpex.LiveResource
  def item_actions do
    [
      show: %{
        module: Backpex.ItemActions.Show,
        label: "查看详情",
        icon: "hero-eye"
      },
      edit: %{
        module: Backpex.ItemActions.Edit,
        label: "编辑",
        icon: "hero-pencil-square"
      },
      delete: %{
        module: Backpex.ItemActions.Delete,
        label: "删除",
        icon: "hero-trash",
        confirm_label: "确认删除",
        confirm_text: "确定要删除此奖励配置吗？此操作不可撤销。",
        only: fn _assigns -> true end
      }
    ]
  end

  @impl Backpex.LiveResource
  def filters do
    [
      reward_type: %{
        module: Teen.Filters.RewardTypeFilter,
        label: "奖励类型"
      }
    ]
  end

  @impl Backpex.LiveResource
  def render_resource_slot(_assigns, item, field, type) do
    case {field, type} do
      {:task_type, :index} ->
        task_type_display(item.task_type)

      {:reward_type, :index} ->
        reward_type_display(item.reward_type)

      {:probability, :index} ->
        "#{Float.round(Decimal.to_float(item.probability) * 100, 1)}%"

      _ ->
        nil
    end
  end

  # 私有函数

  defp task_type_display(task_type) do
    case task_type do
      :invite_register -> "邀请注册"
      :invite_recharge -> "邀请充值"
      :invite_play -> "邀请游戏"
      :invite_retention -> "邀请留存"
      _ -> "未知"
    end
  end

  defp reward_type_display(reward_type) do
    case reward_type do
      :coins -> "金币"
      :points -> "积分"
      :cash -> "现金"
      :wheel_spins -> "转盘次数"
      :items -> "道具"
      _ -> "未知"
    end
  end
end

defmodule Teen.ResourceActions.DeleteByActivity do
  @moduledoc """
  根据活动ID批量删除邀请奖励配置的操作
  """

  use Backpex.ResourceAction

  @impl Backpex.ResourceAction
  def fields do
    [
      activity_id: %{
        module: Backpex.Fields.Text,
        label: "活动ID",
        help_text: "输入要删除奖励配置的活动ID",
        required: true
      }
    ]
  end

  @impl Backpex.ResourceAction
  def handle(socket, data) do
    %{activity_id: activity_id} = data

    try do
      # 使用 Ash 的批量删除功能
      case Teen.ActivitySystem.InviteRewardConfig.destroy_by_activity(%{activity_id: activity_id}) do
        :ok ->
          socket =
            socket
            |> Phoenix.LiveView.put_flash(:info, "成功删除活动的所有奖励配置")
            |> Phoenix.LiveView.assign(selected_items: [])

          {:ok, socket}

        {:error, error} ->
          socket = Phoenix.LiveView.put_flash(socket, :error, "删除失败: #{inspect(error)}")
          {:error, socket}
      end
    rescue
      error ->
        socket = Phoenix.LiveView.put_flash(socket, :error, "删除过程中发生错误: #{inspect(error)}")
        {:error, socket}
    end
  end
end

defmodule Teen.ResourceActions.DeleteByActivity do
  @moduledoc """
  根据活动ID批量删除邀请奖励配置的操作
  """

  use Backpex.ResourceAction

  @impl Backpex.ResourceAction
  def fields do
    [
      activity_id: %{
        module: Backpex.Fields.Text,
        label: "活动ID",
        help_text: "输入要删除奖励配置的活动ID",
        required: true
      }
    ]
  end

  @impl Backpex.ResourceAction
  def handle(socket, data) do
    %{activity_id: activity_id} = data

    # 验证 activity_id 格式
    case validate_activity_id(activity_id) do
      {:ok, validated_id} ->
        try do
          # 使用 Ash 的批量删除功能
          case Teen.ActivitySystem.InviteRewardConfig.destroy_by_activity(validated_id) do
            :ok ->
              socket =
                socket
                |> Phoenix.LiveView.put_flash(:info, "成功删除活动 #{validated_id} 的所有奖励配置")
                |> Phoenix.LiveView.assign(selected_items: [])

              {:ok, socket}

            {:error, error} ->
              socket = Phoenix.LiveView.put_flash(socket, :error, "删除失败: #{inspect(error)}")
              {:error, socket}
          end
        rescue
          error ->
            socket = Phoenix.LiveView.put_flash(socket, :error, "删除过程中发生错误: #{inspect(error)}")
            {:error, socket}
        end

      {:error, message} ->
        socket = Phoenix.LiveView.put_flash(socket, :error, message)
        {:error, socket}
    end
  end

  # 验证活动ID格式
  defp validate_activity_id(activity_id) when is_binary(activity_id) do
    activity_id = String.trim(activity_id)

    if activity_id == "" do
      {:error, "活动ID不能为空"}
    else
      # 验证是否为有效的UUID格式
      case Ecto.UUID.cast(activity_id) do
        {:ok, uuid} -> {:ok, uuid}
        :error -> {:error, "活动ID格式无效，请输入有效的UUID"}
      end
    end
  end

  defp validate_activity_id(_) do
    {:error, "活动ID必须是字符串"}
  end
end

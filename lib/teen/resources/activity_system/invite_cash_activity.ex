defmodule Teen.ActivitySystem.InviteCashActivity do
  @moduledoc """
  拼多多邀请提现（Free Cash）资源

  管理邀请活动配置
  包括奖励总金额、初始奖励范围等
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.ActivitySystem,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [:id, :title, :description, :total_reward, :initial_min, :initial_max, :start_date, :end_date, :status, :updated_at]
  end

  postgres do
    table "invite_cash_activities"
    repo Cypridina.Repo
  end

  code_interface do
    define :create
    define :create_with_configs
    define :read
    define :get_with_configs
    define :list_with_configs
    define :list_active_with_configs
    define :update
    define :update_with_configs
    define :destroy
    define :list_active_activities
    define :enable_activity
    define :disable_activity
  end

  actions do
    defaults [:read]

    destroy :destroy do
      primary? true
      require_atomic? false

      # 使用 change 来级联删除相关的奖励配置
      change fn changeset, _context ->
        activity = changeset.data

        # 确保活动有ID且已加载
        if activity.id do
          try do
            # 先删除所有相关的奖励配置
            activity_id_str = to_string(activity.id)
            case Teen.ActivitySystem.InviteRewardConfig.destroy_by_activity(activity_id_str) do
              :ok ->
                # 奖励配置删除成功，继续删除活动本身
                changeset
              {:error, reason} ->
                # 奖励配置删除失败，阻止删除活动
                Ash.Changeset.add_error(changeset, field: :base, message: "删除相关奖励配置失败，活动删除已取消: #{inspect(reason)}")
            end
          rescue
            error ->
              # 捕获异常并添加错误，阻止删除活动
              Ash.Changeset.add_error(changeset, field: :base, message: "删除过程中发生错误，活动删除已取消: #{inspect(error)}")
          end
        else
          # 如果活动没有ID，直接继续删除操作
          changeset
        end
      end
    end

    create :create do
      accept [:title, :description, :total_reward, :initial_min, :initial_max, :start_date, :end_date, :status]

      change fn changeset, _context ->
        changeset
        |> Ash.Changeset.change_attribute(:status, :enabled)
      end
    end

    update :update do
      accept [:title, :description, :total_reward, :initial_min, :initial_max, :start_date, :end_date, :status]
      require_atomic? false
    end

    create :create_with_configs do
      primary? true
      accept [:title, :description, :total_reward, :initial_min, :initial_max, :start_date, :end_date, :status]

      argument :invite_reward_configs, {:array, :map} do
        allow_nil? true
        description "奖励配置列表"
      end

      change fn changeset, _context ->
        changeset |> Ash.Changeset.change_attribute(:status, :enabled)
      end

      change manage_relationship(:invite_reward_configs,
        type: :direct_control,
        on_missing: :destroy,
        on_match: :update,
        on_no_match: :create
      )
    end

    update :update_with_configs do
      accept [:title, :description, :total_reward, :initial_min, :initial_max, :start_date, :end_date, :status]
      require_atomic? false

      argument :invite_reward_configs, {:array, :map} do
        allow_nil? true
        description "奖励配置列表"
      end

      change manage_relationship(:invite_reward_configs,
        type: :direct_control,
        on_missing: :destroy,
        on_match: :update,
        on_no_match: :create
      )
    end

    read :list_active_activities do
      filter expr(status == :enabled)
    end

    read :get_with_configs do
      get? true
      prepare build(load: [:invite_reward_configs])
    end

    read :list_with_configs do
      prepare build(load: [:invite_reward_configs])
      prepare build(sort: [inserted_at: :desc])
    end

    read :list_active_with_configs do
      filter expr(status == :enabled)
      prepare build(load: [:invite_reward_configs])
      prepare build(sort: [inserted_at: :desc])
    end

    update :enable_activity do
      require_atomic? false
      change set_attribute(:status, :enabled)
    end

    update :disable_activity do
      require_atomic? false
      change set_attribute(:status, :disabled)
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :title, :string do
      allow_nil? false
      public? true
      description "标题"
      constraints max_length: 100
    end

    attribute :description, :string do
      allow_nil? true
      public? true
      description "活动描述"
      constraints max_length: 500
    end

    attribute :total_reward, :decimal do
      allow_nil? false
      public? true
      description "奖励总金额（分）"
      constraints min: Decimal.new("0")
    end

    attribute :initial_min, :decimal do
      allow_nil? false
      public? true
      description "初始最小值（分）"
      constraints min: Decimal.new("0")
    end

    attribute :initial_max, :decimal do
      allow_nil? false
      public? true
      description "初始最大值（分）"
      constraints min: Decimal.new("0")
    end

    attribute :start_date, :date do
      allow_nil? true
      public? true
      description "开始日期"
    end

    attribute :end_date, :date do
      allow_nil? true
      public? true
      description "结束日期"
    end

    attribute :status, :atom do
      allow_nil? false
      public? true
      description "状态"
      constraints one_of: [:enabled, :disabled]
      default :enabled
    end

    timestamps()
  end

  relationships do
    has_many :invite_reward_configs, Teen.ActivitySystem.InviteRewardConfig do
      public? true
      source_attribute :id
      destination_attribute :activity_id
    end
  end
end

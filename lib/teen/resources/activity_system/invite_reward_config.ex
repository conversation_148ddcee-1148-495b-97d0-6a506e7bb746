defmodule Teen.ActivitySystem.InviteRewardConfig do
  @moduledoc """
  邀请奖励配置资源

  管理邀请活动的奖励配置
  包括轮次、奖励类型、奖励范围等
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.ActivitySystem,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [:id, :activity_id, :round_number, :task_type, :reward_type, :min_reward, :max_reward, :required_progress, :probability, :sort_order, :updated_at]
  end

  postgres do
    table "invite_reward_configs"
    repo Cypridina.Repo
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_by_activity
    define :list_by_round
    define :list_by_task_type
    define :list_by_reward_type
    define :get_config
  end

  @doc """
  批量删除指定活动的所有奖励配置
  """
  def destroy_by_activity(activity_id) when is_binary(activity_id) do
    require Logger
    Logger.info("开始删除活动 #{activity_id} 的奖励配置")

    # 将字符串转换为 UUID
    case Ecto.UUID.cast(activity_id) do
      {:ok, uuid} ->
        case list_by_activity(uuid) do
          {:ok, configs} ->
            if Enum.empty?(configs) do
              Logger.info("没有找到活动 #{activity_id} 的奖励配置")
              :ok
            else
              Logger.info("找到 #{length(configs)} 个奖励配置，开始批量删除")

              # 使用 Ash.bulk_destroy 进行批量删除
              case Ash.bulk_destroy(configs, :destroy, %{},
                     return_errors?: true,
                     return_records?: false,
                     atomic?: false) do
                %Ash.BulkResult{status: :success} = result ->
                  Logger.info("成功删除 #{result.count || length(configs)} 个奖励配置")
                  :ok

                %Ash.BulkResult{status: :error, errors: errors} ->
                  Logger.error("批量删除失败: #{inspect(errors)}")
                  {:error, "批量删除失败: #{inspect(errors)}"}

                {:error, reason} ->
                  Logger.error("删除操作失败: #{inspect(reason)}")
                  {:error, "删除操作失败: #{inspect(reason)}"}
              end
            end

          {:error, reason} ->
            Logger.error("查询奖励配置失败: #{inspect(reason)}")
            {:error, "查询奖励配置失败: #{inspect(reason)}"}
        end

      :error ->
        Logger.error("无效的活动ID格式: #{activity_id}")
        {:error, "无效的活动ID格式"}
    end
  end

  def destroy_by_activity(%{activity_id: activity_id}) do
    destroy_by_activity(activity_id)
  end

  actions do
    defaults [:read]

    create :create do
      primary? true
      accept [:activity_id, :round_number, :task_type, :reward_type, :reward_amount, :min_reward, :max_reward, :required_progress, :probability, :description, :sort_order]

      validate present([:activity_id, :round_number, :task_type, :reward_type, :min_reward, :max_reward])
      validate numericality(:round_number, greater_than: 0)
      validate numericality(:required_progress, greater_than: 0)
      validate numericality(:probability, greater_than: 0, less_than_or_equal_to: 1)
    end

    update :update do
      primary? true
      accept [:activity_id, :round_number, :task_type, :reward_type, :reward_amount, :min_reward, :max_reward, :required_progress, :probability, :description, :sort_order]

      validate numericality(:round_number, greater_than: 0)
      validate numericality(:required_progress, greater_than: 0)
      validate numericality(:probability, greater_than: 0, less_than_or_equal_to: 1)
    end

    destroy :destroy do
      primary? true
      require_atomic? false
    end



    read :list_by_activity do
      argument :activity_id, :uuid, allow_nil?: false
      filter expr(activity_id == ^arg(:activity_id))
      prepare build(sort: [:round_number])
    end

    read :list_by_round do
      argument :round_number, :integer, allow_nil?: false
      filter expr(round_number == ^arg(:round_number))
    end

    read :list_by_task_type do
      argument :task_type, :atom, allow_nil?: false
      filter expr(task_type == ^arg(:task_type))
      prepare build(sort: [:round_number])
    end

    read :list_by_reward_type do
      argument :reward_type, :atom, allow_nil?: false
      filter expr(reward_type == ^arg(:reward_type))
      prepare build(sort: [:round_number])
    end

    read :get_config do
      argument :activity_id, :uuid, allow_nil?: false
      argument :round_number, :integer, allow_nil?: false
      filter expr(activity_id == ^arg(:activity_id) and round_number == ^arg(:round_number))
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :activity_id, :uuid do
      allow_nil? false
      public? true
      description "活动ID"
    end

    attribute :round_number, :integer do
      allow_nil? false
      public? true
      description "轮次"
      constraints min: 1
    end

    attribute :task_type, :atom do
      allow_nil? false
      public? true
      description "任务类型"
      constraints one_of: [:invite_register, :invite_recharge, :invite_play, :invite_retention]
      default :invite_register
    end

    attribute :reward_type, :atom do
      allow_nil? false
      public? true
      description "奖励类型"
      constraints one_of: [:coins, :points, :cash, :wheel_spins, :items]
      default :coins
    end

    attribute :reward_amount, :decimal do
      allow_nil? true
      public? true
      description "奖励金额（分）"
      constraints min: Decimal.new("0")
    end

    attribute :min_reward, :decimal do
      allow_nil? false
      public? true
      description "奖励最小值（分）"
      constraints min: Decimal.new("0")
    end

    attribute :max_reward, :decimal do
      allow_nil? false
      public? true
      description "奖励最大值（分）"
      constraints min: Decimal.new("0")
    end

    attribute :required_progress, :integer do
      allow_nil? false
      public? true
      description "完成任务所需进度"
      constraints min: 1
      default 1
    end

    attribute :probability, :decimal do
      allow_nil? false
      public? true
      description "奖励获得概率"
      constraints min: Decimal.new("0"), max: Decimal.new("1")
      default Decimal.new("1.0")
    end

    attribute :description, :string do
      allow_nil? true
      public? true
      description "奖励描述"
      constraints max_length: 500
    end

    attribute :sort_order, :integer do
      allow_nil? false
      public? true
      description "排序顺序"
      default 0
    end

    timestamps()
  end

  relationships do
    belongs_to :activity, Teen.ActivitySystem.InviteCashActivity do
      public? true
      source_attribute :activity_id
      destination_attribute :id
    end
  end

  identities do
    identity :unique_activity_round, [:activity_id, :round_number]
  end
end
